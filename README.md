## Startup steps
1. Local docker env.

    `$ docker build -t zero2prod:latest .`
2. Minikube docker
- Enter minikube docker env.

    `$ eval $(minikube docker-env)`
- Load image from local docker env.
        
    `$ minikube image load zero2prod`
3. Apply k8s manifest.

    `$ kubectl apply -f zero2prod.yaml`
4. migrate database.
- `$ kubectl port-forward svc/postgres-service 5432:5432`
- `$ DATABASE_URL=postgres://postgres:password@127.0.0.1:5432/newsletter?sslmode=disable sqlx migrate run`
5. Test.
- `$ kubectl port-forward svc/zero2prod-service 8000:8000`
- `$ http :8000/health_check`


## K8S commands.
- `$ kubectl get pods`
- `$ kubectl get svc`
- `$ kubectl get deployments`
- `$ kubectl logs <pod-name>`
- `$ kubectl describe pod <pod-name>`
- `$ kubectl delete pod <pod-name>`
- `$ kubectl delete service <service-name>`
- `$ kubectl delete deployment <deployment-name>`
- `$ kubectl delete -f zero2prod.yaml`
- `$ kubectl apply -f zero2prod.yaml`