[package]
name = "zero2prod"
version = "0.1.0"
edition = "2024"

[dependencies]
reqwest = {version = "0.12.15", default-features = false, features = ["json", "rustls-tls"]}
actix-web = "4.11.0"
chrono = { version = "0.4.41", default-features = false, features = ["clock"] }
config = "0.15.11"
once_cell = "1.21.3"
secrecy = { version = "0.10.3", features = ["serde"] }
serde = { version = "1.0.219", features = ["derive"] }
serde-aux = "4.7.0"
sqlx = { version = "0.8.6", features = [
    "runtime-tokio-rustls",
    "macros",
    "postgres",
    "uuid",
    "chrono",
    "migrate",
] }
tokio = { version = "1.45.0", features = ["macros", "rt-multi-thread"] }
tracing = { version = "0.1.41", features = ["log"] }
tracing-actix-web = "0.7.18"
tracing-bunyan-formatter = "0.3.10"
tracing-error = "0.2.1"
tracing-log = "0.2.0"
tracing-subscriber = { version = "0.3.19", features = [
    "registry",
    "env-filter",
] }
unicode-segmentation = "1.12.0"
uuid = { version = "1.17.0", features = ["v4"] }
validator = "0.20.0"
wiremock = "0.6.3"
rand = { version = "0.8.5", features = ["std_rng"] }

[lib]
path = "src/lib.rs"

[[bin]]
name = "zero2prod"
path = "src/main.rs"

[dev-dependencies]
claims = "0.8.0"
fake = "4.3.0"
linkify = "0.10.0"
quickcheck = "1.0.3"
quickcheck_macros = "1.1.0"
rand = "0.8.5"
serde_json = "1.0.140"
tokio = { version = "1.45.0", features = ["macros", "rt"] }
wiremock = "0.6.3"
