---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: zero2prod
  labels:
    app: zero2prod
spec:
  replicas: 1
  selector:
    matchLabels:
      app: zero2prod
  template:
    metadata:
      labels:
        app: zero2prod
      annotations:
        kubectl.kubernetes.io/restartedAt: "2025-06-13T10:14:08+05:30"
    spec:
      containers:
        - name: zero2prod
          image: zero2prod:latest
          imagePullPolicy: Never
          env:
            - name: APP_DATABASE__USERNAME
              value: "postgres"
            - name: APP_DATABASE__PASSWORD
              value: "password"
            - name: APP_DATABASE__PORT
              value: "5432"
            - name: APP_DATABASE__HOST
              value: postgres-service
            - name: APP_DATABASE__NAME
              value: newsletter
            - name: APP_APPLICATION__BASE_URL
              value: http://127.0.0.1
          ports:
            - containerPort: 8000
          readinessProbe:
            httpGet:
              path: /health_check
              port: 8000
            initialDelaySeconds: 5
            periodSeconds: 10
          resources:
            requests:
              memory: "128Mi"
              cpu: "250m"
            limits:
              memory: "256Mi"
              cpu: "500m"

---
apiVersion: v1
kind: Service
metadata:
  name: zero2prod-service
spec:
  selector:
    app: zero2prod
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8000
  type: NodePort

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  labels:
    app: postgres
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
        - name: postgres
          image: postgres:latest
          imagePullPolicy: Never
          env:
            - name: POSTGRES_USER
              value: "postgres"
            - name: POSTGRES_PASSWORD
              value: "password"
            - name: POSTGRES_DB
              value: "newsletter"
          ports:
            - containerPort: 5432
          resources:
            requests:
              memory: "256Mi"
              cpu: "250m"
            limits:
              memory: "512Mi"
              cpu: "500m"
          volumeMounts:
            - mountPath: /var/lib/postgresql/data
              name: postgres-storage
      volumes:
        - name: postgres-storage
          emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
spec:
  selector:
    app: postgres
  ports:
    - protocol: TCP
      port: 5432
      targetPort: 5432
  type: NodePort
